{"timestamp": "2025-06-12T13:41:31.084Z", "query": "Show me the Button component and how to add a new variant called 'success' with green styling", "executionTime": 10347, "snippetsCount": 3, "additionalFilesCount": 2, "totalLines": 71, "snippets": [{"filePath": "src/components/ui/button.tsx", "type": "unknown", "context": "This snippet defines the buttonVariants using cva, including the variant options. To add a new 'success' variant with green styling, this is the primary place to add the new variant key and its styles.", "score": 1, "lines": 39, "startLine": 1, "endLine": 45, "symbols": ["buttonVariants (variant definitions)"], "preview": "import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n..."}, {"filePath": "src/components/ui/button.tsx", "type": "unknown", "context": "This snippet shows the Button component implementation that uses the buttonVariants to apply styles based on the variant prop. It is essential to understand how the variant prop is consumed and how the className is constructed.", "score": 1, "lines": 20, "startLine": 1, "endLine": 65, "symbols": ["Button component"], "preview": "import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n..."}, {"filePath": "src/components/ui/button.tsx", "type": "context", "context": "This snippet shows the ButtonProps interface extending the variant props from buttonVariants, which is important to understand how variant types are derived and used in the Button component.", "score": 0.6, "lines": 12, "startLine": 1, "endLine": 51, "symbols": ["ButtonProps interface"], "preview": "import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n..."}], "additionalFiles": [{"fileName": "src/components/ui/button.tsx", "reason": "May contain related functionality", "suggestedQuery": "Show specific functions in src/components/ui/button.tsx related to: Show me the Button component and how to add a new variant called 'success' with green styling"}, {"fileName": "src/components/ui/button.tsx", "reason": "May contain related functionality", "suggestedQuery": "Show specific functions in src/components/ui/button.tsx related to: Show me the Button component and how to add a new variant called 'success' with green styling"}], "fullResults": {"snippets": [{"filePath": "src/components/ui/button.tsx", "content": "import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\n// Main snippet\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-primary/10 bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        accent:\n          \"bg-accent text-accent-foreground shadow-sm hover:bg-accent/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        xs: \"h-6 px-2 rounded-md text-xs\",\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n);", "startLine": 1, "endLine": 45, "type": "unknown", "symbols": ["buttonVariants (variant definitions)"], "score": 1, "context": "This snippet defines the buttonVariants using cva, including the variant options. To add a new 'success' variant with green styling, this is the primary place to add the new variant key and its styles.", "includesImports": true}, {"filePath": "src/components/ui/button.tsx", "content": "import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\n// Main snippet\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n        data-oid=\"v9r3jpb\"\n      />\n    );\n  },\n);", "startLine": 1, "endLine": 65, "type": "unknown", "symbols": ["Button component"], "score": 1, "context": "This snippet shows the Button component implementation that uses the buttonVariants to apply styles based on the variant prop. It is essential to understand how the variant prop is consumed and how the className is constructed.", "includesImports": true}, {"filePath": "src/components/ui/button.tsx", "content": "import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\n// Main snippet\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}", "startLine": 1, "endLine": 51, "type": "context", "symbols": ["ButtonProps interface"], "score": 0.6, "context": "This snippet shows the ButtonProps interface extending the variant props from buttonVariants, which is important to understand how variant types are derived and used in the Button component.", "includesImports": true}], "additionalFiles": [{"fileName": "src/components/ui/button.tsx", "reason": "May contain related functionality", "suggestedQuery": "Show specific functions in src/components/ui/button.tsx related to: Show me the Button component and how to add a new variant called 'success' with green styling"}, {"fileName": "src/components/ui/button.tsx", "reason": "May contain related functionality", "suggestedQuery": "Show specific functions in src/components/ui/button.tsx related to: Show me the Button component and how to add a new variant called 'success' with green styling"}]}}
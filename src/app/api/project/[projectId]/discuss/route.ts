import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getLatestDiscussChatByProjectId, getProjectById } from '@/lib/db/queries';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const session = await auth();
    const anonymousId = request.headers.get('x-anonymous-id');
    const userId = session?.user.id || anonymousId;

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { projectId } = await params;
    
    // Get the project to verify ownership
    const project = await getProjectById({ id: projectId });
    
    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }
    
    // Check if user owns the project
    if (project.userId !== userId && session?.user?.email !== "<EMAIL>") {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }
    
    // Get the latest discuss chat for this project
    const discussChat = await getLatestDiscussChatByProjectId({ id: projectId });
    
    return NextResponse.json({ discussChat });
  } catch (error) {
    console.error('Error fetching latest discuss chat:', error);
    return NextResponse.json(
      { error: 'Failed to fetch latest discuss chat' },
      { status: 500 }
    );
  }
}

import React, {useState, useRef, useEffect} from 'react';
import {But<PERSON>} from "@/components/ui/button";
import {<PERSON>, <PERSON>rkles, MessageSquare, Loader2, Plus, ChevronDown, Brain, Bug, FileText} from "lucide-react";
import {<PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle} from "@/components/ui/dialog";
import {toast} from "sonner";
import {generateUUID, fetcher, cn} from "@/lib/utils";
import {FileNode} from "@/types/file";
import {useStores} from "@/stores/utils/useStores";
import {useChat} from 'ai/react';
import type {Message, Attachment} from 'ai';
import { convertToUIMessages } from '@/lib/utils';
import { Message as DBMessage } from '@/lib/db/schema';
import {Markdown} from "@/components/base/markdown";
import {MultimodalInput} from "@/components/base/multimodal-input";
import {observer} from "mobx-react-lite";
import { motion, AnimatePresence } from 'framer-motion';
import useSWR from 'swr';
import { Chat } from '@/lib/db/schema';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface DiscussWithAIProps {
    isOpen: boolean;
    onClose: () => void;
    initialMessage?: string;
    type?: 'error-fix' | 'code-review' | 'general-discussion';
    onApplySolution?: (solution: string) => void;
    relevantFiles?: FileNode[];
    metadata?: Record<string, any>;
    chatId: string;
    projectId: string;
}

export const DiscussWithAI = observer(({
                                           isOpen,
                                           onClose,
                                           initialMessage = '',
                                           type = 'error-fix',
                                           onApplySolution,
                                           relevantFiles = [],
                                           metadata = {},
                                           chatId,
                                           projectId
                                       }: DiscussWithAIProps) => {
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const {generatorStore, logStore} = useStores();

    // State for chat management with persistence
    const [selectedChatId, setSelectedChatId] = useState<string | null>(null);

    // Persist chat type selection in localStorage
    const getStoredChatType = (): 'error-fix' | 'code-review' | 'general-discussion' => {
        if (typeof window !== 'undefined') {
            const stored = localStorage.getItem('discuss-chat-type');
            if (stored && ['error-fix', 'code-review', 'general-discussion'].includes(stored)) {
                return stored as 'error-fix' | 'code-review' | 'general-discussion';
            }
        }
        return type; // fallback to prop
    };

    const [selectedType, setSelectedType] = useState<'error-fix' | 'code-review' | 'general-discussion'>(getStoredChatType);

    // Update localStorage when type changes
    const updateSelectedType = (newType: 'error-fix' | 'code-review' | 'general-discussion') => {
        setSelectedType(newType);
        if (typeof window !== 'undefined') {
            localStorage.setItem('discuss-chat-type', newType);
        }
    };

    // Get active session if chatId is provided
    const session = chatId ? generatorStore.getActiveSession(chatId) : null;

    // Generate a unique ID for this discussion
    const [discussionId] = useState(() => generateUUID());

    // Track if initial message has been set
    const initialMessageSetRef = useRef(false);

    // Fetch discuss chats for this project
    const { data: discussChatsData, mutate: mutateDiscussChats } = useSWR<{ discussChats: Chat[] }>(
        projectId ? `/api/project/${projectId}/discuss` : null,
        fetcher,
        {
            fallbackData: { discussChats: [] }
        }
    );

    // Fetch messages for the selected chat
    const { data: messagesData, isLoading: isLoadingMessages } = useSWR<{ messages: DBMessage[] }>(
        selectedChatId ? `/api/chats/${selectedChatId}/messages?all=true` : null,
        fetcher,
        {
            fallbackData: { messages: [] }
        }
    );

    // Convert DB messages to UI messages
    const existingMessages = messagesData?.messages ? convertToUIMessages(messagesData.messages) : [];

    // Format initial message based on type
    const getFormattedPrompt = (message: string) => {
        if (type === 'error-fix') {
            return `I encountered an error: ${message}

Please analyze this error carefully and help me fix it. Consider:
1. The specific error message and its root cause
2. Any related code that might be affected
3. Dependencies that might be missing or misconfigured
4. Similar patterns in the codebase that work correctly
5. Provide a detailed solution with specific code changes

Please provide a detailed solution.`;
        } else if (type === 'code-review') {
            return `I'd like your opinion on this code: ${message}

Please review this code and provide feedback on:
1. Code quality and best practices
2. Potential bugs or edge cases
3. Performance considerations
4. Readability and maintainability
5. Suggestions for improvement

Please be specific in your feedback.`;
        }
        return message;
    };

    // Get relevant files from the session if not provided
    const filesToSend = relevantFiles.length > 0 ? relevantFiles :
        (session?.fileTree || []);

    // State for attachments
    const [attachments, setAttachments] = useState<Attachment[]>([]);

    // Use selected chat ID or create new one
    const currentChatId = selectedChatId || discussionId;

    // Initialize chat with existing messages if a chat is selected
    const {
        messages,
        input,
        setInput,
        handleSubmit,
        isLoading,
        append,
        stop,
        setMessages
    } = useChat({
        id: currentChatId,
        api: '/api/chat',
        initialMessages: selectedChatId ? existingMessages : [],
        body: {
            files: filesToSend,
            activeFile: '',
            dependencies: {},
            linkSupabaseProjectId: '',
            linkSupabaseConnection: '',
            projectId,
            logs: logStore.getLogs(chatId),
            agentModeEnabled: false,
            isReload: false,
            isInitial: false,
            componentContexts: [],
            isAutoFixed: false,
            isDiscussion: true,
            type: selectedType,
            metadata
        },
        onError: (error) => {
            console.error('Error in discussion:', error);
            toast.error('Failed to get a response. Please try again.');
        }
    });

    // Update messages when switching to an existing chat
    useEffect(() => {
        if (selectedChatId && existingMessages.length > 0 && !isLoadingMessages) {
            setMessages(existingMessages);
        } else if (!selectedChatId) {
            setMessages([]);
        }
    }, [selectedChatId, existingMessages, isLoadingMessages, setMessages]);

    // Refresh discuss chats when a new message is sent
    useEffect(() => {
        if (messages.length > 0 && !isLoading) {
            // Refresh the discuss chats list to update titles and timestamps
            mutateDiscussChats();
        }
    }, [messages.length, isLoading, mutateDiscussChats]);

    // Auto-load latest discussion chat when dialog opens
    useEffect(() => {
        if (isOpen && discussChatsData?.discussChats && discussChatsData.discussChats.length > 0 && !selectedChatId && !initialMessage) {
            // Only auto-load if no chat is selected and no initial message is provided
            const latestChat = discussChatsData.discussChats[0]; // Already sorted by updatedAt desc
            setSelectedChatId(latestChat.id);
            initialMessageSetRef.current = true; // Prevent initial message from being added
        }
    }, [isOpen, discussChatsData?.discussChats, selectedChatId, initialMessage]);

    // Reset state when dialog closes
    useEffect(() => {
        if (!isOpen) {
            setSelectedChatId(null);
            initialMessageSetRef.current = false;
        }
    }, [isOpen]);

    // Set initial message only once when dialog opens and no chat is selected
    useEffect(() => {
        if (isOpen && initialMessage && !initialMessageSetRef.current && messages.length === 0 && !selectedChatId) {
            // Mark that we've set the initial message
            initialMessageSetRef.current = true;

            // Add the initial message
            append({
                role: 'user',
                content: getFormattedPrompt(initialMessage),
                id: generateUUID()
            });

            // Clear the input
            setInput('');
        }
    }, [isOpen, initialMessage, messages.length, append, setInput, getFormattedPrompt, selectedChatId]);

    // Scroll to bottom of messages
    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({behavior: 'smooth'});
    }, [messages]);


    const handleApplySolution = () => {
        // Get the last assistant message
        const lastAssistantMessage = [...messages].reverse().find(msg => msg.role === 'assistant');

        if (lastAssistantMessage && onApplySolution) {
            onApplySolution(lastAssistantMessage.content);
            onClose();
            toast.success('Solution applied to main chat!');
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        // Send message on Ctrl+Enter or Cmd+Enter
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            handleSubmit(e as any);
        }
    };

    // Helper functions for chat management
    const createNewChat = () => {
        setSelectedChatId(null);
        initialMessageSetRef.current = false;
        setMessages([]);
    };

    const selectChat = (chat: Chat) => {
        setSelectedChatId(chat.id);
        initialMessageSetRef.current = true; // Prevent initial message from being added to existing chat
    };

    const getTypeIcon = (chatType: string) => {
        switch (chatType) {
            case 'error-fix':
                return <Bug className="h-4 w-4" />;
            case 'code-review':
                return <FileText className="h-4 w-4" />;
            default:
                return <Brain className="h-4 w-4" />;
        }
    };

    const getTypeLabel = (chatType: string) => {
        switch (chatType) {
            case 'error-fix':
                return 'Fix Error';
            case 'code-review':
                return 'Code Review';
            default:
                return 'General Discussion';
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="sm:max-w-[900px] h-[85vh] p-0 bg-background/95 backdrop-blur-xl border-border/50 overflow-hidden flex flex-col" disableCloseButton>
                <div className="relative flex flex-col h-full">
                    {/* Background gradient */}
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-accent/5 to-chart-3/5" />

                    <div className="relative z-10 flex flex-col h-full">
                        {/* Header */}
                        <div className="px-4 sm:px-6 py-3 border-b border-border/50 bg-background/50 backdrop-blur-sm flex-shrink-0">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                    <div className="h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center">
                                        {getTypeIcon(selectedType)}
                                    </div>
                                    <div>
                                        <h2 className="text-lg font-semibold text-foreground">Plan with AI</h2>
                                        <p className="text-sm text-muted-foreground">
                                            {getTypeLabel(selectedType)} • {discussChatsData?.discussChats?.length || 0} conversations
                                            {selectedChatId && (
                                                <span className="ml-2 text-primary">
                                                    • {discussChatsData?.discussChats?.find(c => c.id === selectedChatId) ? 'Continuing' : 'Loading'}
                                                </span>
                                            )}
                                        </p>
                                    </div>
                                </div>
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={onClose}
                                    className="h-8 w-8 rounded-full"
                                >
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>

                        {/* Chat Controls */}
                        <div className="px-4 sm:px-6 py-3 border-b border-border/30 bg-background/30 backdrop-blur-sm flex-shrink-0">
                            <div className="flex items-center gap-3">
                                {/* Chat Selector */}
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button
                                            variant="outline"
                                            className={cn(
                                                "flex items-center gap-2 min-w-[200px] justify-between",
                                                isLoadingMessages && "opacity-50"
                                            )}
                                            disabled={isLoadingMessages}
                                        >
                                            <span className="truncate">
                                                {isLoadingMessages ? (
                                                    <span className="flex items-center gap-2">
                                                        <Loader2 className="h-3 w-3 animate-spin" />
                                                        Loading...
                                                    </span>
                                                ) : selectedChatId ? (
                                                    discussChatsData?.discussChats?.find(c => c.id === selectedChatId)?.title || 'Untitled Chat'
                                                ) : (
                                                    'New Conversation'
                                                )}
                                            </span>
                                            <ChevronDown className="h-4 w-4" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="start" className="w-[300px]">
                                        <DropdownMenuItem onClick={createNewChat} className="flex items-center gap-2">
                                            <Plus className="h-4 w-4" />
                                            <span>New Conversation</span>
                                        </DropdownMenuItem>
                                        {discussChatsData?.discussChats && discussChatsData.discussChats.length > 0 && (
                                            <>
                                                <DropdownMenuSeparator />
                                                {discussChatsData.discussChats.map((chat) => (
                                                    <DropdownMenuItem
                                                        key={chat.id}
                                                        onClick={() => selectChat(chat)}
                                                        className={cn(
                                                            "flex items-center gap-2",
                                                            selectedChatId === chat.id && "bg-primary/10"
                                                        )}
                                                    >
                                                        <MessageSquare className="h-4 w-4" />
                                                        <div className="flex flex-col flex-1 min-w-0">
                                                            <span className="truncate">{chat.title || 'Untitled Chat'}</span>
                                                            <span className="text-xs text-muted-foreground">
                                                                {new Date(chat.updatedAt).toLocaleDateString()}
                                                            </span>
                                                        </div>
                                                    </DropdownMenuItem>
                                                ))}
                                            </>
                                        )}
                                    </DropdownMenuContent>
                                </DropdownMenu>

                                {/* Type Selector */}
                                <Select value={selectedType} onValueChange={updateSelectedType}>
                                    <SelectTrigger className="w-[180px]">
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="error-fix">
                                            <div className="flex items-center gap-2">
                                                <Bug className="h-4 w-4" />
                                                <span>Fix Error</span>
                                            </div>
                                        </SelectItem>
                                        <SelectItem value="code-review">
                                            <div className="flex items-center gap-2">
                                                <FileText className="h-4 w-4" />
                                                <span>Code Review</span>
                                            </div>
                                        </SelectItem>
                                        <SelectItem value="general-discussion">
                                            <div className="flex items-center gap-2">
                                                <Brain className="h-4 w-4" />
                                                <span>General Discussion</span>
                                            </div>
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        {/* Messages Area */}
                        <div className="flex-1 overflow-y-auto bg-background/20 backdrop-blur-sm">
                            <div className="p-4 sm:p-6 space-y-4">
                                <AnimatePresence mode="wait">
                                    {isLoadingMessages && selectedChatId && (
                                        <motion.div
                                            initial={{ opacity: 0 }}
                                            animate={{ opacity: 1 }}
                                            exit={{ opacity: 0 }}
                                            className="text-center py-12"
                                        >
                                            <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                                                <Loader2 className="h-6 w-6 animate-spin text-primary"/>
                                            </div>
                                            <h3 className="text-lg font-medium text-foreground mb-2">
                                                Loading conversation...
                                            </h3>
                                            <p className="text-muted-foreground">
                                                Fetching messages from your previous discussion
                                            </p>
                                        </motion.div>
                                    )}

                                    {messages.length === 0 && !isLoadingMessages && (
                                        <motion.div
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            exit={{ opacity: 0, y: -20 }}
                                            className="text-center py-12"
                                        >
                                            <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                                                {getTypeIcon(selectedType)}
                                            </div>
                                            <h3 className="text-lg font-medium text-foreground mb-2">
                                                {getTypeLabel(selectedType)}
                                            </h3>
                                            <p className="text-muted-foreground max-w-md mx-auto">
                                                {selectedType === 'error-fix' && 'Describe the error you\'re experiencing in detail for better results.'}
                                                {selectedType === 'code-review' && 'Share your code for a comprehensive review and feedback.'}
                                                {selectedType === 'general-discussion' && 'Start a conversation about your project, architecture, or any development questions.'}
                                            </p>
                                        </motion.div>
                                    )}
                                </AnimatePresence>

                                {messages.map((msg, index) => (
                                    <motion.div
                                        key={msg.id}
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ delay: index * 0.1 }}
                                        className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
                                    >
                                        <div
                                            className={cn(
                                                'p-4 rounded-2xl backdrop-blur-sm border',
                                                msg.role === 'user'
                                                    ? 'bg-primary text-primary-foreground border-primary/20 max-w-[80%] ml-12'
                                                    : 'bg-background/80 border-border/50 max-w-[90%] mr-12'
                                            )}
                                        >
                                            {msg.role === 'user' ? (
                                                <p className="whitespace-pre-wrap text-sm">{msg.content}</p>
                                            ) : (
                                                <div className="text-sm markdown-content">
                                                    <Markdown>{msg.content as string}</Markdown>
                                                </div>
                                            )}
                                        </div>
                                    </motion.div>
                                ))}

                                {isLoading && (
                                    <motion.div
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        className="flex justify-start"
                                    >
                                        <div className="bg-background/80 border border-border/50 rounded-2xl p-4 backdrop-blur-sm">
                                            <div className="flex items-center gap-2">
                                                <Loader2 className="h-4 w-4 animate-spin text-primary"/>
                                                <span className="text-sm text-muted-foreground">AI is thinking...</span>
                                            </div>
                                        </div>
                                    </motion.div>
                                )}

                                <div ref={messagesEndRef}/>
                            </div>
                        </div>

                        {/* Input Area */}
                        <div className="border-t border-border/50 bg-background/50 backdrop-blur-sm flex-shrink-0">
                            <div className="p-4 sm:p-6">
                                <MultimodalInput
                                    chatId={currentChatId}
                                    projectId={"discuss-" + currentChatId}
                                    input={input}
                                    setInput={setInput}
                                    handleSubmit={handleSubmit}
                                    isLoading={isLoading}
                                    stop={stop}
                                    attachments={attachments}
                                    setAttachments={setAttachments}
                                    messages={messages}
                                    inDesignMode={true}
                                    setMessages={() => {}}
                                    append={append}
                                    componentContexts={[]}
                                    onRemoveComponentContext={() => {}}
                                    onClearComponentContexts={() => {}}
                                    onVisualSelectionClicked={() => {}}
                                    selectMode={false}
                                />

                                {messages.length > 0 && onApplySolution && (
                                    <motion.div
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        className="mt-4"
                                    >
                                        <Button
                                            onClick={handleApplySolution}
                                            variant="default"
                                            className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 border-0 shadow-lg"
                                        >
                                            <Sparkles className="h-4 w-4 mr-2"/>
                                            Apply Solution to Main Chat
                                        </Button>
                                    </motion.div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
});

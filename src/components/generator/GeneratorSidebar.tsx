'use client';

import { usePathname, useRouter } from 'next/navigation';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import {MessageSquare, Box, Settings, PlusCircle, Home, FolderIcon, Layers, HomeIcon, PlusIcon} from 'lucide-react';
import {clearTime<PERSON>, observer} from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
import { useAuth } from '@/hooks/use-auth';
import { Chat, Project } from '@/lib/db/schema';
import useSWR from 'swr';
import {fetcher, generateUUID} from '@/lib/utils';
import { SubscriptionIndicator } from '@/components/subscription/subscription-indicator';

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarSeparator,
  SidebarGroup,
  SidebarGroupContent,
  useSidebar,
} from '@/components/ui/sidebar';
import { SidebarUserNav } from '@/components/base/sidebar-user-nav';
import {<PERSON><PERSON>} from "@/components/ui/button";
import SidebarSkeletonLoader from "@/components/ui/sidebar-skeleton-loader";
import {generateProjectAttributes} from "@/app/(project)/projects/actions";
import MagicallyLogo from "@/components/logo";
import {useLatestDiscussChat} from "@/hooks/use-latest-discuss-chat";

interface GeneratorSidebarProps {
  chatId?: string;
  projectId: string
}

export const GeneratorSidebar = observer(({ chatId, projectId }: GeneratorSidebarProps) => {
  const pathname = usePathname();
  const { open, setOpen, setOpenMobile } = useSidebar();
  const { session } = useAuth();
  const { generatorStore } = useStores();
  const router = useRouter();
  
  // Effect to adjust chat and preview panel widths when sidebar opens/closes
  useEffect(() => {
    // Get the document root element to set CSS variables
    const root = document.documentElement;
    
    if (open) {
      // When sidebar is open, reduce chat and preview widths by 10% each
      root.style.setProperty('--chat-width-adjustment', '90%');
      root.style.setProperty('--preview-width-adjustment', '90%');
    } else {
      // When sidebar is closed, restore full widths
      root.style.setProperty('--chat-width-adjustment', '100%');
      root.style.setProperty('--preview-width-adjustment', '100%');
    }
  }, [open]);
  
  // Function to close sidebar on navigation
  const handleNavigation = () => {
    close();
  };

  const generateNewChat = () => {
    const newId = generateUUID();
    router.push(`/projects/${projectId}/chats/new`);
    handleNavigation();
  }


  // Get the current chat session
  // const currentSession = chatId ? generatorStore.getActiveSession(chatId) : null;

  const { data: projectData, isLoading: loadingProjectData } = useSWR<Project>(
      `/api/project/${projectId}`,
      fetcher,
  );

  const { data: chats, isLoading: chatLoading } = useSWR<Chat[]>(
      `/api/project/${projectId}/chat`,
      fetcher,
      {
        fallbackData: [],
      }
  );

  // Fetch the latest discuss chat for this project
  const { discussChat, isLoading: discussChatLoading } = useLatestDiscussChat({
    projectId,
    enabled: !!projectId
  });

  useEffect(() => {
    let timeout: any;
    if(projectData && projectData.id) {
      timeout = setTimeout(() => {
        setOpen(false);
        setOpenMobile(false);
      }, 3000);

    }
    return () => {
      if(timeout) {
        clearTimeout(timeout);
      }
    };
  }, [projectData]);

  const close = () => {
    setOpen(false);
    setOpenMobile(false);
  }

  return (
    <Sidebar>
      <SidebarHeader className="border-b border-sidebar-border">
        <div className="px-3 py-2">
          <div className="flex items-center justify-between">
            <Link 
              href="/"
              className="flex items-center gap-2 hover:opacity-80 transition-opacity"
              onClick={handleNavigation}
            >
              <MagicallyLogo logoWidthAction={32} asLink/>
              {/*<h2 className="text-lg font-semibold">magically</h2>*/}
            </Link>
            <SubscriptionIndicator variant="compact" />
          </div>
        </div>
      </SidebarHeader>
      
      <SidebarContent className="py-2">
        {/* Main Navigation */}
        <SidebarMenu className="px-2 space-y-1">
          <SidebarMenuItem>
            <SidebarMenuButton 
              asChild 
              isActive={pathname === '/'}
            >
              <Link href="/" onClick={handleNavigation}>
                <Home className="h-4 w-4" />
                <span>Dashboard</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          
          <SidebarMenuItem>
            <SidebarMenuButton 
              asChild 
              isActive={pathname ? (pathname === '/projects') : false}
            >
              <Link href="/projects" onClick={handleNavigation}>
                <Layers className="h-4 w-4" />
                <span>All Projects</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={pathname ? (pathname === '/') : false}
            >
              <Link href="/" onClick={handleNavigation}>
                <PlusIcon className="h-4 w-4" />
                <span>New Project</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>

        {
          loadingProjectData ?
              <SidebarSkeletonLoader/>:
              <>
                {projectData ? (
                    <>
                      <SidebarSeparator className="my-3" />

                      {/* Project Header */}
                      <div className="px-3 py-2">
                        <div className="flex items-center gap-2 mb-2">
                          <FolderIcon className="h-4 w-4 text-sidebar-foreground/70" />
                          <h3 className="text-sm font-semibold text-sidebar-foreground truncate">
                            {projectData.appName || 'Untitled Project'}
                          </h3>
                        </div>
                        {projectData.description && (
                            <p className="text-xs text-sidebar-foreground/70 mb-2 px-1 truncate">
                              {projectData.description}
                            </p>
                        )}
                      </div>

                      {/* Project Navigation */}
                      <SidebarMenu className="px-2 space-y-1">
                        {/* Project Home Section */}
                        <SidebarMenuItem>
                          <SidebarMenuButton
                              asChild
                              isActive={pathname && projectId ? pathname === `/projects/${projectId}` : false}
                          >
                            <Link href={`/projects/${projectId}`} onClick={handleNavigation}>
                              <HomeIcon className="h-4 w-4" />
                              <span>Project Dashboard</span>
                            </Link>
                          </SidebarMenuButton>
                        </SidebarMenuItem>

                        {/* Chats Section */}
                        <SidebarMenuItem>
                          <SidebarMenuButton
                              asChild
                              isActive={pathname && projectId ? pathname === `/projects/${projectId}/chats` : false}
                          >
                            <Link href={`/projects/${projectId}/chats`} onClick={handleNavigation}>
                              <MessageSquare className="h-4 w-4" />
                              <span>Chats</span>
                            </Link>
                          </SidebarMenuButton>
                        </SidebarMenuItem>

                        {/* Deployments Section */}
                        <SidebarMenuItem>
                          <SidebarMenuButton
                              asChild
                              isActive={pathname && projectId ? pathname.includes(`/projects/${projectId}/deployments`) : false}
                          >
                            <Link href={`/projects/${projectId}/deployments`} onClick={handleNavigation}>
                              <Box className="h-4 w-4" />
                              <span>Deployments</span>
                            </Link>
                          </SidebarMenuButton>
                        </SidebarMenuItem>

                        {/* Settings Section */}
                        <SidebarMenuItem>
                          <SidebarMenuButton
                              asChild
                              isActive={pathname && projectId ? pathname.includes(`/projects/${projectId}/settings`) : false}
                          >
                            <Link href={`/projects/${projectId}/settings`} onClick={handleNavigation}>
                              <Settings className="h-4 w-4" />
                              <span>Settings</span>
                            </Link>
                          </SidebarMenuButton>
                        </SidebarMenuItem>
                      </SidebarMenu>

                      {/* Project Chats List */}
                      <SidebarGroup>
                        <SidebarGroupContent>
                          <SidebarMenu>
                            <div className="px-3 py-1 text-xs text-sidebar-foreground/70 font-medium mt-2">
                              Recent Chats
                            </div>

                            {/* New Chat Button */}
                            <SidebarMenuItem>
                              <SidebarMenuButton
                                  asChild
                                  className="mt-2 border-black text-black dark:border-white border dark:text-white hover:bg-primary"
                              >
                                <Link href={`/projects/${projectId}/chats/new`} onClick={handleNavigation}>
                                  <PlusCircle className="h-4 w-4" />
                                  <span className="text-xs">New Chat for {projectData.appName}</span>
                                </Link>
                              </SidebarMenuButton>
                            </SidebarMenuItem>

                            {/* Latest Discuss Chat */}
                            {discussChat && (
                              <SidebarMenuItem>
                                <SidebarMenuButton
                                  asChild
                                  isActive={chatId === discussChat.id}
                                  className="bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800"
                                >
                                  <Link href={`/projects/${projectId}/chats/${discussChat.id}`} onClick={handleNavigation}>
                                    <MessageSquare className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                    <span className="truncate text-xs text-blue-700 dark:text-blue-300">
                                      💡 {discussChat.title || 'Plan with AI'}
                                    </span>
                                  </Link>
                                </SidebarMenuButton>
                              </SidebarMenuItem>
                            )}

                            {
                              chatLoading ?
                                  <SidebarSkeletonLoader/>
                                  : <>
                                    { chats && chats?.length > 0 ? (
                                        chats?.map(chat => (
                                            <SidebarMenuItem key={chat.id}>
                                              <SidebarMenuButton
                                                  asChild
                                                  isActive={chatId === chat.id}
                                              >
                                                <Link href={`/projects/${projectId}/${chat.type === "app" ? "chats": "design"}/${chat.id}`} onClick={handleNavigation}>
                                                  <span className="truncate text-xs">{chat.title || 'Untitled Chat'}</span>
                                                </Link>
                                              </SidebarMenuButton>
                                            </SidebarMenuItem>
                                        ))
                                    ) : (
                                        <div className="px-4 py-2 text-xs text-sidebar-foreground/50 italic">
                                          No chats in this project
                                        </div>
                                    )}
                                  </>
                            }


                          </SidebarMenu>
                        </SidebarGroupContent>
                      </SidebarGroup>
                    </>
                ) :  <div className="px-4 py-2 text-xs text-sidebar-foreground/50 italic text-red-400">
                  Project data could not be loaded. Please refresh
                </div>}
              </>
        }
        {/* Current Project Section */}

      </SidebarContent>
      
      <SidebarFooter className="border-t border-sidebar-border">
        {!projectData && (
          <SidebarMenu className="px-2 py-2">
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <Button onClick={generateNewChat}>
                  <PlusCircle className="h-4 w-4" />
                  <span>New Project</span>
                </Button>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        )}
        {projectData && (
          <div className="px-2 py-2 border-b border-sidebar-border">
            <SubscriptionIndicator variant="sidebar" />
          </div>
        )}
        {session?.user && <SidebarUserNav user={session?.user} />}
      </SidebarFooter>
    </Sidebar>
  );
});

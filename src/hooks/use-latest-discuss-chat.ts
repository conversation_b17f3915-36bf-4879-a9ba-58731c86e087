import { useState, useEffect } from 'react';
import { Chat } from '@/lib/db/schema';

interface UseLatestDiscussChatProps {
  projectId: string;
  enabled?: boolean;
}

interface UseLatestDiscussChatReturn {
  discussChat: Chat | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useLatestDiscussChat({ 
  projectId, 
  enabled = true 
}: UseLatestDiscussChatProps): UseLatestDiscussChatReturn {
  const [discussChat, setDiscussChat] = useState<Chat | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDiscussChat = async () => {
    if (!enabled || !projectId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/project/${projectId}/discuss`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch discuss chat: ${response.status}`);
      }

      const data = await response.json();
      setDiscussChat(data.discussChat);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch discuss chat';
      setError(errorMessage);
      console.error('Error fetching latest discuss chat:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDiscussChat();
  }, [projectId, enabled]);

  return {
    discussChat,
    isLoading,
    error,
    refetch: fetchDiscussChat
  };
}

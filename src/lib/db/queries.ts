import 'server-only';

import { genSaltSync, hashSync } from 'bcrypt-ts';
import { and, asc, count, desc, eq, gt, gte, inArray, lt, lte, ne, or, sql } from 'drizzle-orm';
import { randomUUID } from 'crypto';

import {
  user,
  chat,
  type User,
  document,
  type Suggestion,
  suggestion,
  type Message,
  message,
  vote,
  passwordResetTokens, fileState, FileState,
  projects, type Project, Chat,
  stream, screenshotState,
  temperatureOptimization, type TemperatureOptimization,
} from './schema';
import { apkBuilds, deployments, subscriptions as subscriptionsSchema } from './schema';
import {PgSelectBase, PgSelectWithout} from "drizzle-orm/pg-core";
import {BlockKind} from "@/components/base/block";
import dayjs from "dayjs";

// Import the singleton database client
import { db } from './db';

// Utility function for retrying database operations
async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries = 3,
  initialDelay = 300
): Promise<T> {
  let lastError;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      console.log(`Retry attempt ${attempt + 1}/${maxRetries} failed:`, error);
      lastError = error;

      // Don't wait on the last attempt
      if (attempt < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, initialDelay * Math.pow(2, attempt)));
      }
    }
  }

  throw lastError;
}

export async function getUser(email: string): Promise<Array<User>> {
  try {
    return await db.select().from(user).where(eq(user.email, email));
  } catch (error) {
    console.error('Failed to get user from database');
    throw error;
  }
}

export async function getUserById(id: string): Promise<User | undefined> {
  try {
    const users = await db.select().from(user).where(eq(user.id, id));
    return users[0];
  } catch (error) {
    console.error('Failed to get user by ID from database');
    throw error;
  }
}

export async function createOrFetchAnonUser(id: string): Promise<User> {
  try {
    // Try to find existing anonymous user
    const existingUser = await getUserById(id);
    if (existingUser) {
      return existingUser;
    }

    // Create new anonymous user
    const [newUser] = await db.insert(user).values({
      id,
      email: `anon-${id}@temp.magically.life`,
      isAnonymous: true,
    }).returning();

    return newUser;
  } catch (error) {
    console.error('Failed to create/fetch anonymous user');
    throw error;
  }
}

export async function createUser(
  email: string,
  password: string,
  name: string,
  provider: 'credentials' | 'google' = 'credentials'
): Promise<User> {
  try {
    const hashedPassword = password ? hashSync(password, genSaltSync(10)) : null;
    const now = new Date();

    const [createdUser] = await db
      .insert(user)
      .values({
        email,
        password: hashedPassword,
        name,
        provider,
        createdAt: now,
        updatedAt: now
      })
      .returning();

    return createdUser;
  } catch (error) {
    console.error('Failed to create user in database', error);
    throw error;
  }
}

export async function saveChat({
  id,
  userId,
  projectId,
  title,
  updatedAt,
  isInitialized,
  type
}: {
  id: string;
  userId: string;
  projectId?: string;
  title: string;
  updatedAt: Date,
  isInitialized?: boolean,
  type?: 'app' | 'design' | 'discuss'
}) {
  try {
    const chats = await db.insert(chat).values({
      id,
      createdAt: new Date(),
      updatedAt,
      userId,
      projectId,
      title,
      isInitialized,
      type: type || 'app' // Default to 'app' if not specified
    }).returning();
    return chats[0];
  } catch (error) {
    console.error('Failed to save chat in database', error);
    throw error;
  }
}

export async function markChatAsInitialized({ id }: { id: string }) {
  try {
    await db
        .update(chat)
        .set({
          isInitialized: true,
        })
        .where(eq(chat.id, id));
  } catch (error) {
    console.error('Failed to update chat in database', error);
  }
}

/**
 * Create a new chat
 */
export async function createChat({
    id,
  title,
  userId,
  projectId,
  visibility = 'private',
  type = 'app',
  connectionId,
}: {
  id: string;
  title: string;
  userId: string;
  projectId?: string;
  visibility?: 'public' | 'private' | 'hidden';
  type?: 'app' | 'design' | 'admin' | 'discuss';
  connectionId?: string;
}) {
  try {
    const now = new Date();
    const [newChat] = await db
      .insert(chat)
      .values({
        id,
        title,
        userId,
        projectId,
        visibility,
        type,
        connectionId,
        createdAt: now,
        updatedAt: now,
      })
      .returning();

    return newChat;
  } catch (error) {
    console.error('Failed to create chat in database', error);
    throw error;
  }
}

/**
 * Update an existing chat
 */
export async function updateChat({
  id,
  updatedAt,
  isInitialized,
  designHtml,
  designStatus,
  isDesignApproved,
  needsContinuation,
  title,
}: {
  id: string;
  updatedAt: Date;
  isInitialized?: boolean;
  designHtml?: string;
  designStatus?: 'initializing' | 'generating' | 'complete' | 'error';
  isDesignApproved?: boolean;
  needsContinuation?: boolean;
  title?: string;
}) {
  try {
    // Build the update object with only provided fields
    const updateData: any = { updatedAt };

    if (isInitialized !== undefined) {
      updateData.isInitialized = isInitialized;
    }

    if (designHtml !== undefined) {
      updateData.designHtml = designHtml;
    }

    if (designStatus !== undefined) {
      updateData.designStatus = designStatus;
    }

    if (isDesignApproved !== undefined) {
      updateData.isDesignApproved = isDesignApproved;
    }

    if (typeof needsContinuation !== "undefined") {
      updateData.needsContinuation = needsContinuation;
    }

    if (title !== undefined) {
      updateData.title = title;
    }
    const chats = await db
      .update(chat)
      .set(updateData)
      .where(eq(chat.id, id))
      .returning();

    return chats[0];
  } catch (error) {
    console.error('Failed to update chat in database', error);
    throw error;
  }
}

export async function deleteChatById({ id }: { id: string }) {
  try {
    await db.delete(vote).where(eq(vote.chatId, id));
    await db.delete(message).where(eq(message.chatId, id));

    return await db.delete(chat).where(eq(chat.id, id));
  } catch (error) {
    console.error('Failed to delete chat by id from database');
    throw error;
  }
}

export async function getChatsByUserId({ id }: { id: string }) {
  try {
    return await db
      .select()
      .from(chat)
      .where(eq(chat.userId, id))
      .orderBy(desc(chat.createdAt))
      .limit(20);
  } catch (error) {
    console.error('Failed to get chats by user from database');
    throw error;
  }
}

export async function getChatById({ id }: { id: string }): Promise<Chat> {
  try {
    const [selectedChat] = await db.select().from(chat).where(eq(chat.id, id));
    return selectedChat as Chat;
  } catch (error) {
    console.error('Failed to get chat by id from database');
    throw error;
  }
}

export async function saveMessages({ messages }: { messages: Array<Message> }) {
  try {
    return await db.insert(message).values(messages).returning();
  } catch (error) {
    console.error('Failed to save messages in database', error);
    throw error;
  }
}

export async function getMessagesByChatId({ id }: { id: string }) {
  try {
    return await db
      .select()
      .from(message)
      .where(eq(message.chatId, id))
      .orderBy(asc(message.createdAt));
  } catch (error) {
    console.error('Failed to get messages by chat ID from database');
    throw error;
  }
}

export async function getMessagesByChatIdForChat({ id }: { id: string }, {limit}:{limit?:  number} = {}) {
  try {
    const messages =  await db
      .select()
      .from(message)
      .where(eq(message.chatId, id))
      .orderBy(desc(message.createdAt))
      .limit(limit || 1000);

    return messages.reverse();
  } catch (error) {
    console.error('Failed to get messages by chat ID from database');
    throw error;
  }
}

/**
 * Get the first 20 user messages and all messages between them for a chat
 * This is used for the initial load of a chat page
 * @param id - The chat ID
 * @returns An object containing messages and metadata
 */
export async function getInitialMessagesByChatId({ id }: { id: string }) {
  try {
    // Get total count of user messages for this chat
    const [result] = await db
      .select({ count: count() })
      .from(message)
      .where(and(
        eq(message.chatId, id),
        eq(message.role, 'user')
      ));

    const totalUserMessages = Number(result.count);
    console.log(`[DB] Total user messages for chat ${id}: ${totalUserMessages}`);

    // First, find the first 20 user messages
    const userMessages = await db
      .select()
      .from(message)
      .where(and(
        eq(message.chatId, id),
        eq(message.role, 'user')
      ))
      .orderBy(desc(message.createdAt))
      .limit(20);

    if (userMessages.length === 0) {
      return {
        messages: [],
        totalUserMessages: 0,
        hasMoreMessages: false
      };
    }

    // Get the timestamps of the first and last user messages
    const newestTimestamp = userMessages[0].createdAt;
    const oldestTimestamp = userMessages[userMessages.length - 1].createdAt;

    // Get all messages between the oldest and newest user messages (including them)
    const allMessagesInRange = await db
      .select()
      .from(message)
      .where(and(
        eq(message.chatId, id),
        gte(message.createdAt, oldestTimestamp)
      ))
      .orderBy(asc(message.createdAt));

    console.log(`[DB] Loaded ${allMessagesInRange.length} messages, including ${userMessages.length} user messages`);

    // Determine if there are more messages to load
    const hasMoreMessages = totalUserMessages > userMessages.length;

    return {
      messages: allMessagesInRange,
      totalUserMessages,
      hasMoreMessages
    };
  } catch (error) {
    console.error('Failed to get initial messages by chat ID from database', error);
    return {
      messages: [],
      totalUserMessages: 0,
      hasMoreMessages: false
    };
  }
}

/**
 * Get messages by chat ID with pagination support
 * This function fetches user messages and all messages between them
 * @param chatId - The chat ID
 * @param limit - Number of user messages to fetch (will include all messages between them)
 * @param cursor - Message ID to use as cursor for pagination
 * @param direction - Sort direction ('asc' or 'desc')
 */
export async function getMessagesByChatIdPaginated({
  chatId,
  limit = 20,
  cursor = null,
  direction = 'desc',
  skip = 0
}: {
  chatId: string;
  limit?: number;
  cursor?: string | null;
  direction?: 'asc' | 'desc';
  skip?: number;
}) {
  try {
    console.log(`[DB] Fetching messages with direction=${direction}, limit=${limit}, cursor=${cursor}`);

    // First, find user messages with pagination
    let userMessagesQuery = db
      .select()
      .from(message)
      .where(and(
        eq(message.chatId, chatId),
        eq(message.role, 'user')
      ));

    // Apply cursor-based pagination if cursor is provided
    if (cursor) {
      try {
        const cursorMessage = await db
          .select()
          .from(message)
          .where(eq(message.id, cursor))
          .limit(1);

        if (cursorMessage.length > 0) {
          const cursorTimestamp = cursorMessage[0].createdAt;
          console.log(`[DB] Found cursor message with timestamp ${cursorTimestamp}`);

          // Update the where clause to include timestamp comparison
          if (direction === 'desc') {
            // For descending order, get messages created before the cursor
            userMessagesQuery = db
              .select()
              .from(message)
              .where(and(
                eq(message.chatId, chatId),
                eq(message.role, 'user'),
                lt(message.createdAt, cursorTimestamp)
              ));
          } else {
            // For ascending order, get messages created after the cursor
            userMessagesQuery = db
              .select()
              .from(message)
              .where(and(
                eq(message.chatId, chatId),
                eq(message.role, 'user'),
                gt(message.createdAt, cursorTimestamp)
              ));
          }
        }
      } catch (error) {
        console.error('Error applying cursor pagination:', error);
        // Continue without cursor if there's an error
      }
    }

    // Get user messages with limit and skip
    const userMessages = await userMessagesQuery
      .orderBy(direction === 'desc' ? desc(message.createdAt) : asc(message.createdAt))
      .offset(skip) // Skip messages we've already loaded
      .limit(limit + 1); // Fetch one extra to determine if there are more messages

    console.log(`[DB] Fetched ${userMessages.length} user messages with direction=${direction}, skip=${skip}, limit=${limit}`);

    // Check if there are more messages
    const hasMore = userMessages.length > limit;

    // Remove the extra message if there are more
    const limitedUserMessages = hasMore ? userMessages.slice(0, limit) : userMessages;

    if (limitedUserMessages.length === 0) {
      // If no user messages, return empty result
      console.log('[DB] No user messages found, returning empty result');
      return {
        messages: [],
        hasMore: false,
        nextCursor: null
      };
    }

    // For ascending order, the first message is the oldest and the last is the newest
    // For descending order, the first message is the newest and the last is the oldest
    let oldestTimestamp, newestTimestamp;

    if (direction === 'desc') {
      // In descending order, last message is oldest, first is newest
      oldestTimestamp = limitedUserMessages[limitedUserMessages.length - 1].createdAt;
      newestTimestamp = limitedUserMessages[0].createdAt;
    } else {
      // In ascending order, first message is oldest, last is newest
      oldestTimestamp = limitedUserMessages[0].createdAt;
      newestTimestamp = limitedUserMessages[limitedUserMessages.length - 1].createdAt;
    }

    console.log(`[DB] Timestamp range: oldest=${oldestTimestamp}, newest=${newestTimestamp}`);

    // Get all messages between the oldest and newest user messages (including them)
    let allMessagesInRange = await db
      .select()
      .from(message)
      .where(and(
        eq(message.chatId, chatId),
        lte(message.createdAt, newestTimestamp),
        gte(message.createdAt, oldestTimestamp)
      ))
      .orderBy(direction === 'desc' ? desc(message.createdAt) : asc(message.createdAt));

    console.log(`[DB] Fetched ${allMessagesInRange.length} total messages in the timestamp range`);

    // Get the next cursor (ID of the appropriate user message based on direction)
    let nextCursor;
    if (direction === 'desc') {
      // For descending order, the next cursor is the ID of the oldest message
      nextCursor = limitedUserMessages.length > 0 ? limitedUserMessages[limitedUserMessages.length - 1].id : null;
    } else {
      // For ascending order, the next cursor is the ID of the newest message
      nextCursor = limitedUserMessages.length > 0 ? limitedUserMessages[limitedUserMessages.length - 1].id : null;
    }

    console.log(`[DB] Next cursor: ${nextCursor}, hasMore: ${hasMore}`);

    return {
      messages: allMessagesInRange,
      hasMore,
      nextCursor
    };
  } catch (error) {
    console.error('Failed to get paginated messages by chat ID from database', error);
    // Return empty result instead of throwing to prevent cascading failures
    return {
      messages: [],
      hasMore: false,
      nextCursor: null
    };
  }
}

/**
 * Get the latest user message for a chat
 * This is useful for saving dependencies to ensure they're associated with the latest user interaction
 */
export async function getLatestUserMessageByChatId({ chatId }: { chatId: string }) {
  try {
    const messages = await db
      .select()
      .from(message)
      .where(and(
        eq(message.chatId, chatId),
        eq(message.role, 'user')
      ))
      .orderBy(desc(message.createdAt))
      .limit(1);

    return messages[0] || null;
  } catch (error) {
    console.error('Failed to get latest user message by chat ID from database');
    throw error;
  }
}

export async function voteMessage({
  chatId,
  messageId,
  type,
}: {
  chatId: string;
  messageId: string;
  type: 'up' | 'down';
}) {
  try {
    const [existingVote] = await db
      .select()
      .from(vote)
      .where(and(eq(vote.messageId, messageId)));

    if (existingVote) {
      return await db
        .update(vote)
        .set({ isUpvoted: type === 'up' })
        .where(and(eq(vote.messageId, messageId), eq(vote.chatId, chatId)));
    }
    return await db.insert(vote).values({
      chatId,
      messageId,
      isUpvoted: type === 'up',
    });
  } catch (error) {
    console.error('Failed to upvote message in database', error);
    throw error;
  }
}

export async function getVotesByChatId({ id }: { id: string }) {
  try {
    return await db.select().from(vote).where(eq(vote.chatId, id));
  } catch (error) {
    console.error('Failed to get votes by chat id from database', error);
    throw error;
  }
}

export async function saveDocument({
  id,
  title,
  kind,
  content,
  userId,
}: {
  id: string;
  title: string;
  kind: BlockKind;
  content: string;
  userId: string;
}) {
  try {
    return await db.insert(document).values({
      id,
      title,
      kind,
      content,
      userId,
      createdAt: new Date(),
    });
  } catch (error) {
    console.error('Failed to save document in database');
    throw error;
  }
}

export async function getDocumentsById({ id }: { id: string }) {
  try {
    const documents = await db
      .select()
      .from(document)
      .where(eq(document.id, id))
      .orderBy(asc(document.createdAt));

    return documents;
  } catch (error) {
    console.error('Failed to get document by id from database');
    throw error;
  }
}

export async function getDocumentById({ id }: { id: string }) {
  try {
    const [selectedDocument] = await db
      .select()
      .from(document)
      .where(eq(document.id, id))
      .orderBy(desc(document.createdAt));

    return selectedDocument;
  } catch (error) {
    console.error('Failed to get document by id from database');
    throw error;
  }
}

export async function deleteDocumentsByIdAfterTimestamp({
  id,
  timestamp,
}: {
  id: string;
  timestamp: Date;
}) {
  try {
    await db
      .delete(suggestion)
      .where(
        and(
          eq(suggestion.documentId, id),
          gt(suggestion.documentCreatedAt, timestamp),
        ),
      );

    return await db
      .delete(document)
      .where(and(eq(document.id, id), gt(document.createdAt, timestamp)));
  } catch (error) {
    console.error(
      'Failed to delete documents by id after timestamp from database',
    );
    throw error;
  }
}

export async function saveSuggestions({
  suggestions,
}: {
  suggestions: Array<Suggestion>;
}) {
  try {
    return await db.insert(suggestion).values(suggestions);
  } catch (error) {
    console.error('Failed to save suggestions in database');
    throw error;
  }
}

export async function getSuggestionsByDocumentId({
  documentId,
}: {
  documentId: string;
}) {
  try {
    return await db
      .select()
      .from(suggestion)
      .where(and(eq(suggestion.documentId, documentId)));
  } catch (error) {
    console.error(
      'Failed to get suggestions by document version from database',
    );
    throw error;
  }
}

export async function getMessageById({ id }: { id: string }) {
  try {
    return await db.select().from(message).where(eq(message.id, id));
  } catch (error) {
    console.error('Failed to get message by id from database');
    throw error;
  }
}

export async function deleteMessagesByChatIdAfterTimestamp({
  chatId,
  timestamp,
}: {
  chatId: string;
  timestamp: Date;
}) {
  try {
    return await db
      .delete(message)
      .where(
        and(eq(message.chatId, chatId), gt(message.createdAt, timestamp)),
      );
  } catch (error) {
    console.error(
      'Failed to delete messages by id after timestamp from database',
    );
    throw error;
  }
}

export async function updateChatVisiblityById({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: 'private' | 'public' | 'hidden';
}) {
  try {
    return await db.update(chat).set({ visibility }).where(eq(chat.id, chatId));
  } catch (error) {
    console.error('Failed to update chat visibility in database');
    throw error;
  }
}

export async function createPasswordResetToken({
  userId,
  token,
  expiresAt,
}: {
  userId: string;
  token: string;
  expiresAt: Date;
}) {
  return db
      .insert(passwordResetTokens)
      .values({
        userId,
        token,
        expiresAt,
      })
      .returning();
}

export async function getPasswordResetToken(token: string) {
  return db
      .select()
      .from(passwordResetTokens)
      .where(
          and(
              eq(passwordResetTokens.token, token),
              gt(passwordResetTokens.expiresAt, new Date())
          )
      )
      .limit(1);
}

export async function updateUserPassword({
  userId,
  hashedPassword,
}: {
  userId: string;
  hashedPassword: string;
}) {
  return db
      .update(user)
      .set({password: hashedPassword})
      .where(eq(user.id, userId))
      .returning();
}

export async function deletePasswordResetToken(token: string) {
  return db
      .delete(passwordResetTokens)
      .where(eq(passwordResetTokens.token, token));
}

export async function saveFileState({
  chatId,
  projectId,
  messageId,
  files,
  dependencies
}: {
  chatId: string;
  projectId?: string;
  messageId: string;
  files: any;
  dependencies: Record<string, any>
}) {
  try {
    // If projectId is not provided, get it from the chat
    if (!projectId) {
      const chatData = await getChatById({ id: chatId });
      projectId = chatData.projectId || undefined;
    }

    // Get the latest version for this project
    let nextVersion = 1; // Default to 1 if no previous versions exist

    if (projectId) {
      try {
        const latestFileState = await db.select({ version: fileState.version })
          .from(fileState)
          .where(eq(fileState.projectId, projectId))
          .orderBy(desc(fileState.version))
          .limit(1);

        if (latestFileState.length > 0 && latestFileState[0].version) {
          nextVersion = latestFileState[0].version + 1;
        }
      } catch (versionError) {
        console.warn('Could not determine latest version for project, using default', versionError);
        // Continue with default version 1
      }
    } else {
      console.warn('No projectId available, using default version 1');
    }

    const [fileStateData] = await db.insert(fileState).values({
      id: randomUUID(),
      chatId,
      projectId,
      messageId,
      files: files,
      dependencies,
      createdAt: new Date(),
      version: nextVersion, // Use the incremented version
    }).returning();

    return fileStateData;
  } catch (error) {
    console.error('Failed to save file state', error);
    throw error;
  }
}

export async function getLatestFileState(chatId: string): Promise<FileState> {
  const [latestState] = await db.select()
    .from(fileState)
    .where(eq(fileState.chatId, chatId))
    .orderBy(desc(fileState.createdAt))
    .limit(1);

  return latestState;
}

export async function getLatestFileStateByProject(projectId: string): Promise<FileState> {
  const [latestState] = await db.select()
    .from(fileState)
    .where(eq(fileState.projectId, projectId))
    .orderBy(desc(fileState.createdAt))
    .limit(1);

  return latestState;
}

export async function deleteFileStateByMessageId(messageId: string) {
  return db
    .delete(fileState)
    .where(eq(fileState.messageId, messageId));
}

export async function batchDeleteFileStatesByMessageIds(messageIds: string[]) {
  if (messageIds.length === 0) return { count: 0 };

  return db
    .delete(fileState)
    .where(inArray(fileState.messageId, messageIds));
}

export async function markAsBaseCacheVersion(chatId: string, fileStateId: string) {
  // First, unmark any existing base cache versions
  await db.update(fileState)
    .set({ isBaseCacheVersion: false })
    .where(eq(fileState.chatId, chatId));

  // Then mark this one as the base
  await db.update(fileState)
    .set({ isBaseCacheVersion: true })
    .where(eq(fileState.id, fileStateId));
}

export async function getBaseCacheVersion(chatId: string): Promise<FileState> {
  const [baseVersion] = await db.select()
    .from(fileState)
    .where(and(
      eq(fileState.chatId, chatId),
      eq(fileState.isBaseCacheVersion, true)
    ))
    .limit(1);

  return baseVersion;
}

export async function getMessagesSince(chatId: string, timestamp: Date) {
  console.time('getMessagesSince');
  try {
    // Use the existing chatId_createdAt index directly in the query
    // This avoids fetching all messages and filtering in memory
    const messages = await withRetry(async () => {
      return db.select()
          .from(message)
          .where(and(
              eq(message.chatId, chatId),
              gt(message.createdAt, timestamp)
          ))
          .orderBy(asc(message.createdAt));
    });

    console.log(`Found ${messages.length} messages since ${timestamp.toISOString()}`);
    return messages;
  } catch (error) {
    console.error('Error in getMessagesSince:', error);
    // Fallback to empty array in case of error
    return [];
  } finally {
    console.timeEnd('getMessagesSince');
  }
}

export async function saveFileStateAndCacheIfNeeded({
  chatId,
  projectId,
  messageId,
  files,
  dependencies,
  shouldCache = false
}: {
  chatId: string;
  projectId?: string;
  messageId: string;
  files: any;
  dependencies: Record<string, any>;
  shouldCache?: boolean;
}) {
 try {
   console.log('Saving file state')
   console.log('messageId', messageId)
   console.log('files', files?.map((file: any) => file.name))
   // Save the file state
   const savedState = await saveFileState({
     chatId,
     projectId,
     messageId,
     files,
     dependencies
   });

   console.log('Creating new base')
   // Mark as base cache version if needed
   if (shouldCache) {
     await markAsBaseCacheVersion(chatId, savedState.id);
   }

   return savedState;
 } catch (e: any) {
   console.log('ERror saving file state', e)
 }
}

export async function saveScreenShotState({
                                            projectId,
                                            chatId,
                                            messageId,
                                            screenshots
                                          }: {
                                            projectId: string;
                                            chatId: string,
                                            messageId: string,
                                            screenshots: any[],
                                          }) {
  return db.insert(screenshotState).values({
    projectId,
    chatId,
    messageId,
    screenshots
  });
}

export async function deleteVoteByMessageId(messageId: string) {
  return db
    .delete(vote)
    .where(eq(vote.messageId, messageId));
}

export async function batchDeleteVotesByMessageIds(messageIds: string[]) {
  if (messageIds.length === 0) return { count: 0 };

  return db
    .delete(vote)
    .where(inArray(vote.messageId, messageIds));
}

export async function removeLatestFileState(chatId: string) {
  const latestState = await getLatestFileState(chatId);
  if (!latestState) return null;

  await db
    .delete(fileState)
    .where(
      and(
        eq(fileState.chatId, chatId),
        eq(fileState.createdAt, latestState.createdAt)
      )
    );

  return latestState;
}

export async function getMessagesByTimestamp({
  chatId,
  timestamp,
}: {
  chatId: string;
  timestamp: Date;
}) {
  return db
    .select()
    .from(message)
    .where(
      and(
        eq(message.chatId, chatId),
        gt(message.createdAt, timestamp)
      )
    );
}

// Project-related functions
export async function getProjectById({ id }: { id: string }) {
  try {
    const [project] = await db.select().from(projects).where(eq(projects.id, id));
    return project;
  } catch (error) {
    console.error('Failed to get project by id from database');
    throw error;
  }
}

export async function saveProject(saveData: {
  id: string;
  userId: string;
  appName: string;
} & Partial<Project>) {
  try {
    const {
      id,
      userId,
      appName,
      slug,
      scheme,
      bundleIdentifier,
      packageName, ...rest} = saveData;
    const slugValue = slug || appName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');

    const projectData =  await db.insert(projects).values({
      id,
      userId,
      appName: appName,
      slug: slugValue,
      scheme: scheme || appName.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, ''),
      bundleIdentifier: bundleIdentifier || `com.magically.${slugValue}`,
      packageName: packageName || `com.magically.${slugValue}`,
      visibility: 'private',
      createdAt: new Date(),
      updatedAt: new Date(),
      ...rest,
    }).returning();
    return projectData[0] as Project;
  } catch (error) {
    console.error('Failed to save project in database', error);
    throw error;
  }
}

export async function saveBareProject({
                                        id,
                                        userId
                                      }: {
  id: string;
  userId: string;
}) {
  try {
    const slugValue = id;
    const projectData = await db.insert(projects).values({
      id,
      userId,
      slug: slugValue,
      isMigratedv1: false
    }).returning();
    return projectData[0];
  } catch (error) {
    console.error('Failed to save project in database', error);
    throw error;
  }
}

export async function getProjectsByUserId({ id }: { id: string }) {
  return withRetry(async () => {
    try {
      console.log('Fetching projects for user:', id);
      const result = await db
        .select()
        .from(projects)
        .where(eq(projects.userId, id))
        .orderBy(desc(projects.updatedAt))
        .limit(50);
      console.log(`Successfully fetched ${result.length} projects`);
      return result;
    } catch (error) {
      console.error('Failed to get projects by user from database:', error);
      throw error;
    }
  });
}

export async function getChatsByProjectId({ id }: { id: string }) {
  try {
    return await db
      .select()
      .from(chat)
      .where(
        and(
          eq(chat.projectId, id),
          ne(chat.type, 'discuss') // Exclude discuss chats from main list
        )
      )
      .orderBy(desc(chat.updatedAt));
  } catch (error) {
    console.error('Failed to get chats by project from database');
    throw error;
  }
}

export async function getLatestChatsByProjectId({ id }: { id: string }) {
  try {
    return await db
      .select()
      .from(chat)
      .where(
        and(
          eq(chat.projectId, id),
          ne(chat.type, 'discuss') // Exclude discuss chats from main list
        )
      )
      .orderBy(desc(chat.updatedAt))
        .limit(3);
  } catch (error) {
    console.error('Failed to get chats by project from database');
    throw error;
  }
}

export async function getLatestDiscussChatByProjectId({ id }: { id: string }) {
  try {
    const [latestDiscussChat] = await db
      .select()
      .from(chat)
      .where(
        and(
          eq(chat.projectId, id),
          eq(chat.type, 'discuss')
        )
      )
      .orderBy(desc(chat.updatedAt))
      .limit(1);
    return latestDiscussChat || null;
  } catch (error) {
    console.error('Failed to get latest discuss chat by project from database');
    throw error;
  }
}

export async function getFileStatesByProject({ id }: { id: string }) {
  try {
    return await db
      .select()
      .from(fileState)
      .where(eq(fileState.projectId, id))
      .orderBy(desc(fileState.createdAt));
  } catch (error) {
    console.error('Failed to get file states by project from database');
    throw error;
  }
}

export async function updateProject({
  id,
  ...updateData
}: {
  id: string;
} & Partial<Project>) {
  try {
    // Always update the updatedAt timestamp when any change is made
    const dataToUpdate = {
      ...updateData,
      updatedAt: new Date(),
    };

    const updatedProjects = await db
      .update(projects)
      .set(dataToUpdate)
      .where(eq(projects.id, id))
      .returning();

    return updatedProjects[0];
  } catch (error) {
    console.error('Failed to update project in database', error);
    throw error;
  }
}


export async function deleteTrailingMessages({id, isGroupedMessage = false}: { id: string, isGroupedMessage?: boolean }) {
  console.time('deleteTrailingMessages:total');

  console.time('deleteTrailingMessages:getMessageById');
  let [messageToDelete] = await getMessageById({id});
  console.timeEnd('deleteTrailingMessages:getMessageById');

  if(!messageToDelete) {
    console.timeEnd('deleteTrailingMessages:total');
    throw new Error("Message not found");
  }

  // Default to using this message's timestamp
  let timestampToUse = messageToDelete.createdAt;

  if(messageToDelete && messageToDelete.parentAssistantMessageId) {
    // Some mistake happened while sending the message ID. Let's correct it.
    const [correctedMessage] = await getMessageById({id: messageToDelete.parentAssistantMessageId});

    if(!correctedMessage) {
      throw new Error("The message deletion is incorrect and may cause issues with your app. Please contatct us to get this fixed.");
    }
    messageToDelete = correctedMessage;
    timestampToUse = messageToDelete.createdAt;
    isGroupedMessage = true;
  }

  // console.log('message', messageToDelete)

  // If this is a parent message (group head), find the latest child message timestamp
  if (isGroupedMessage && messageToDelete.isAssistantGroupHead) {
    console.log('Finding latest child message for parent:', messageToDelete.id);
    // Get all child messages with this parentAssistantMessageId
    const childMessages = await db
      .select()
      .from(message)
      .where(eq(message.parentAssistantMessageId, messageToDelete.id))
      .orderBy(desc(message.createdAt));

    // If we found child messages, use the timestamp of the latest one
    if (childMessages.length > 0) {
      timestampToUse = childMessages[0].createdAt;
      console.log('Using latest child message timestamp:', timestampToUse);
    }
  }

  // First get all messages that will be deleted
  console.time('deleteTrailingMessages:getMessagesByTimestamp');
  const messagesToDelete = await getMessagesByTimestamp({
    chatId: messageToDelete.chatId,
    timestamp: timestampToUse,
  });
  console.timeEnd('deleteTrailingMessages:getMessagesByTimestamp');
  console.log(`Messages to delete count: ${messagesToDelete.length}`);

  // console.log('messagesToDelete', JSON.stringify(messagesToDelete, null, 2))

  // throw new Error("ASDASD")
  if (messagesToDelete.length === 0) {
    console.timeEnd('deleteTrailingMessages:total');
    throw new Error("No messages found to delete");
  }

  if(messagesToDelete[0].role !== "user") {
    throw new Error("The message deletion is incorrect and may cause issues with your app. Please contact us to get this fixed.");
  }

  // Extract all message IDs
  const messageIds = messagesToDelete.map(msg => msg.id);

  // Batch delete file states and votes
  console.time('deleteTrailingMessages:batchDeleteFileStates');
  await batchDeleteFileStatesByMessageIds(messageIds);
  console.timeEnd('deleteTrailingMessages:batchDeleteFileStates');

  console.time('deleteTrailingMessages:batchDeleteVotes');
  await batchDeleteVotesByMessageIds(messageIds);
  console.timeEnd('deleteTrailingMessages:batchDeleteVotes');

  // Then delete the messages
  console.time('deleteTrailingMessages:deleteMessagesByChatIdAfterTimestamp');
  await deleteMessagesByChatIdAfterTimestamp({
    chatId: messageToDelete.chatId,
    timestamp: timestampToUse,
  });
  console.timeEnd('deleteTrailingMessages:deleteMessagesByChatIdAfterTimestamp');
  let latestMessage: Message | null = null;
  console.time(`deleteTrailingMessages:latestMessage`);
  [latestMessage] = await db
      .select()
      .from(message)
      .where(eq(message.chatId, messageToDelete.chatId))
      .orderBy(desc(message.createdAt))
      .limit(1);
  console.timeEnd(`deleteTrailingMessages:latestMessage`)
  console.timeEnd('deleteTrailingMessages:total');

  return latestMessage?.id || null;
}

export async function getMessageCountForToday(userId: string): Promise<number> {
  try {
    // Get all messages for this user
    const messages = await db
      .select()
      .from(message)
      .where(and(
          eq(message.userId, userId),
          eq(message.role, 'user')
      ));

    // Count messages from today manually
    let count = 0;
    const today = new Date();

    for (const msg of messages) {
      try {
        const msgDate = new Date(msg.createdAt);

        // Check if the message was created today
        if (msgDate.getDate() === today.getDate() &&
            msgDate.getMonth() === today.getMonth() &&
            msgDate.getFullYear() === today.getFullYear()) {
          count++;
        }
      } catch (e) {
        // Skip any messages with invalid dates
        console.error('Error processing message date:', e);
      }
    }

    return count;
  } catch (error) {
    console.error('Failed to get message count for today', error);
    return 0; // Return 0 instead of throwing to avoid breaking the application
  }
}

export async function createStreamId({
                                       streamId,
                                       chatId,
                                     }: {
  streamId: string;
  chatId: string;
}) {
  try {
    await db
        .insert(stream)
        .values({ id: streamId, chatId, createdAt: new Date() });
  } catch (error) {
    throw new Error(
        'bad_request:database : Failed to create stream id'
    );
  }
}

export async function getStreamIdsByChatId({ chatId }: { chatId: string }) {
  try {
    const streamIds = await db
        .select({ id: stream.id })
        .from(stream)
        .where(eq(stream.chatId, chatId))
        .orderBy(asc(stream.createdAt))
        .execute();

    return streamIds.map(({ id }) => id);
  } catch (error) {
    throw new Error(
        'bad_request:database: Failed to get stream ids by chat id',
    );
  }
}

/**
 * Get screenshot data for a project
 * Fetches the latest screenshot metadata from the screenshotState table
 * Falls back to the legacy format if no data is found in the new table
 */
export async function getProjectScreenshots(projectId: string) {
  try {
    // Fetch the screenshot data from the screenshotState table
    const screenshotStateData = await db
      .select()
      .from(screenshotState)
      .where(eq(screenshotState.projectId, projectId))
      .orderBy(desc(screenshotState.createdAt))
      .limit(1);

    if (!screenshotStateData || screenshotStateData.length === 0) {
      // Fallback to the old method if no data in the new table
      const project = await db
        .select()
        .from(projects)
        .where(eq(projects.id, projectId))
        .limit(1);

      if (!project || project.length === 0) {
        return { error: 'Project not found' };
      }

      // No legacy data available since designScreenshotUrls doesn't exist in the schema
      return { screenshots: [] };
    }

    // Return the screenshot metadata directly from the screenshotState table
    return { screenshots: screenshotStateData[0].screenshots };
  } catch (error: any) {
    console.error('Error getting project screenshots:', error);
    return { error: `Error retrieving screenshots: ${error?.message || 'Unknown error'}` };
  }
}

export async function getSubscriptionById(id: string): Promise<typeof subscriptionsSchema.$inferSelect | undefined> {
  try {
    const result = await db.select().from(subscriptionsSchema).where(eq(subscriptionsSchema.id, id)).limit(1);
    return result[0];
  } catch (error) {
    console.error('Failed to get subscription by LemonSqueezy ID from database:', error);
    throw error;
  }
}

export async function updateSubscriptionOnCancel(
  id: string,
  isActive: boolean,
  newMetadata: Record<string, any>,
  currentMetadata?: Record<string, any> | null
) {
  try {
    const combinedMetadata = { ...currentMetadata, ...newMetadata };
    const endsAtFromMeta = newMetadata.endsAt;
    const updates = {
      status: 'cancelled',
      isActive: isActive,
      metadata: combinedMetadata,
      updatedAt: new Date(),
    }
    const resetDateValue = endsAtFromMeta;
    if (resetDateValue) {
      updates[resetDate] = resetDateValue;
    }

    await db
      .update(subscriptionsSchema)
      .set(updates as any)
      .where(eq(subscriptionsSchema.id, id));
  } catch (error) {
    console.error('Failed to update subscription on cancel in database:', error);
    throw error;
  }
}

/**
 * Save temperature optimization metadata
 */
export async function saveTemperatureOptimization({
  messageId,
  chatId,
  projectId,
  userId,
  optimizedTemperature,
  selectedModel,
  reasoning,
  fileCount,
  contextFactors,
  userProgression,
  optimizationDuration,
  wasSuccessful = true,
  errorMessage,
  usedFallback = false,
  fallbackReason,
}: {
  messageId: string;
  chatId: string;
  projectId?: string;
  userId?: string;
  optimizedTemperature: number;
  selectedModel: string;
  reasoning: string;
  contextFactors: string[];
  userProgression?: {
    isProgressing: boolean;
    isStuck: boolean;
    stuckSeverity?: "mild" | "moderate" | "severe";
  };
  fileCount: number;
  optimizationDuration?: number;
  wasSuccessful?: boolean;
  errorMessage?: string;
  usedFallback?: boolean;
  fallbackReason?: string;
}): Promise<TemperatureOptimization> {
  try {
    const [result] = await db
      .insert(temperatureOptimization)
      .values({
        messageId,
        chatId,
        projectId,
        userId,
        optimizedTemperature,
        selectedModel,
        reasoning,
        contextFactors,
        userProgression,
        fileCount,
        optimizationDuration,
        wasSuccessful,
        errorMessage,
        usedFallback,
        fallbackReason,
      })
      .returning();

    return result;
  } catch (error) {
    console.error('Failed to save temperature optimization in database:', error);
    throw error;
  }
}

/**
 * Get temperature optimization history for analysis
 */
export async function getTemperatureOptimizationHistory({
  chatId,
  projectId,
  userId,
  limit = 50,
}: {
  chatId?: string;
  projectId?: string;
  userId?: string;
  limit?: number;
}) {
  try {
    let query = db.select().from(temperatureOptimization);

    const conditions: any[] = [];
    if (chatId) conditions.push(eq(temperatureOptimization.chatId, chatId));
    if (projectId) conditions.push(eq(temperatureOptimization.projectId, projectId));
    if (userId) conditions.push(eq(temperatureOptimization.userId, userId));

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    return await query
      .orderBy(desc(temperatureOptimization.createdAt))
      .limit(limit);
  } catch (error) {
    console.error('Failed to get temperature optimization history:', error);
    throw error;
  }
}
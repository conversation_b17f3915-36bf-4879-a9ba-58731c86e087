import { action, computed, makeAutoObservable, observable, runInAction } from "mobx";
import { Chat } from '@/lib/db/schema';
import { Message } from 'ai';
import { RootStore } from '@/stores/RootStore';

export type DiscussionType = 'error-fix' | 'code-review' | 'general-discussion';

export interface DiscussState {
    isOpen: boolean;
    selectedChatId: string | null;
    selectedType: DiscussionType;
    userWantsNewChat: boolean;
    isLoadingMessages: boolean;
    isLoadingChats: boolean;
    initialMessageSet: boolean;
}

export class DiscussStore {
    private readonly rootStore: RootStore;

    @observable state: DiscussState = {
        isOpen: false,
        selectedChatId: null,
        selectedType: 'general-discussion',
        userWantsNewChat: false,
        isLoadingMessages: false,
        isLoadingChats: false,
        initialMessageSet: false,
    };

    @observable discussChats: Chat[] = [];
    @observable currentMessages: Message[] = [];
    @observable projectId: string | null = null;

    constructor(rootStore: RootStore) {
        this.rootStore = rootStore;
        makeAutoObservable(this);
        this.loadStoredType();
    }

    @action
    openDialog(projectId: string, initialType?: DiscussionType) {
        this.projectId = projectId;
        this.state.isOpen = true;
        this.state.userWantsNewChat = false;
        this.state.initialMessageSet = false;
        
        if (initialType) {
            this.setSelectedType(initialType);
        }
        
        this.loadDiscussChats();
    }

    @action
    closeDialog() {
        this.state.isOpen = false;
        this.state.selectedChatId = null;
        this.state.userWantsNewChat = false;
        this.state.initialMessageSet = false;
        this.currentMessages = [];
        this.projectId = null;
    }

    @action
    setSelectedType(type: DiscussionType) {
        this.state.selectedType = type;
        this.persistType(type);
    }

    @action
    createNewChat() {
        this.state.selectedChatId = null;
        this.state.userWantsNewChat = true;
        this.state.initialMessageSet = false;
        this.currentMessages = [];
    }

    @action
    selectChat(chatId: string) {
        this.state.selectedChatId = chatId;
        this.state.userWantsNewChat = false;
        this.state.initialMessageSet = true;
        this.loadChatMessages(chatId);
    }

    @action
    setInitialMessageSet(value: boolean) {
        this.state.initialMessageSet = value;
    }

    @action
    setMessages(messages: Message[]) {
        this.currentMessages = messages;
    }

    @action
    async loadDiscussChats() {
        if (!this.projectId) return;

        this.state.isLoadingChats = true;
        try {
            const response = await fetch(`/api/project/${this.projectId}/discuss`);
            if (response.ok) {
                const data = await response.json();
                runInAction(() => {
                    this.discussChats = data.discussChats || [];
                    this.autoSelectLatestChat();
                });
            }
        } catch (error) {
            console.error('Failed to load discuss chats:', error);
        } finally {
            runInAction(() => {
                this.state.isLoadingChats = false;
            });
        }
    }

    @action
    async loadChatMessages(chatId: string) {
        this.state.isLoadingMessages = true;
        try {
            const response = await fetch(`/api/chats/${chatId}/messages?all=true`);
            if (response.ok) {
                const data = await response.json();
                const { convertToUIMessages } = await import('@/lib/utils');
                runInAction(() => {
                    this.currentMessages = data.messages ? convertToUIMessages(data.messages) : [];
                });
            }
        } catch (error) {
            console.error('Failed to load chat messages:', error);
        } finally {
            runInAction(() => {
                this.state.isLoadingMessages = false;
            });
        }
    }

    @action
    refreshChats() {
        if (this.projectId) {
            this.loadDiscussChats();
        }
    }

    @action
    private autoSelectLatestChat() {
        if (
            this.discussChats.length > 0 && 
            !this.state.selectedChatId && 
            !this.state.userWantsNewChat
        ) {
            const latestChat = this.discussChats[0]; // Already sorted by updatedAt desc
            this.selectChat(latestChat.id);
        }
    }

    @action
    private loadStoredType() {
        if (typeof window !== 'undefined') {
            const stored = localStorage.getItem('discuss-chat-type');
            if (stored && ['error-fix', 'code-review', 'general-discussion'].includes(stored)) {
                this.state.selectedType = stored as DiscussionType;
            }
        }
    }

    @action
    private persistType(type: DiscussionType) {
        if (typeof window !== 'undefined') {
            localStorage.setItem('discuss-chat-type', type);
        }
    }

    @computed
    get selectedChat(): Chat | null {
        if (!this.state.selectedChatId) return null;
        return this.discussChats.find(chat => chat.id === this.state.selectedChatId) || null;
    }

    @computed
    get shouldShowEmptyState(): boolean {
        return this.currentMessages.length === 0 && !this.state.isLoadingMessages;
    }

    @computed
    get currentChatId(): string {
        return this.state.selectedChatId || this.generateDiscussionId();
    }

    private generateDiscussionId(): string {
        // Generate a consistent ID for new discussions
        return `discuss-${Date.now()}`;
    }

    // Helper methods for UI
    getTypeIcon(type: DiscussionType): string {
        switch (type) {
            case 'error-fix': return 'Bug';
            case 'code-review': return 'FileText';
            default: return 'Brain';
        }
    }

    getTypeLabel(type: DiscussionType): string {
        switch (type) {
            case 'error-fix': return 'Fix Error';
            case 'code-review': return 'Code Review';
            default: return 'General Discussion';
        }
    }

    getEmptyStateMessage(type: DiscussionType): string {
        switch (type) {
            case 'error-fix': 
                return 'Describe the error you\'re experiencing in detail for better results.';
            case 'code-review': 
                return 'Share your code for a comprehensive review and feedback.';
            default: 
                return 'Start a conversation about your project, architecture, or any development questions.';
        }
    }
}
